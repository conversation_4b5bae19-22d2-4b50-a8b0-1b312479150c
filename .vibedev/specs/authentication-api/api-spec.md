# 认证API规格说明

## API概述

本文档定义了Coco服务器认证系统的完整API规格，包括登录、访问令牌管理、用户资料等功能。

## 基础信息

- **Base URL**: `http://localhost:9000`
- **Content-Type**: `application/json`
- **字符编码**: UTF-8

## 认证方式

### 1. JWT Bearer Token认证
```http
Authorization: Bearer <access_token>
```

### 2. API Token认证
```http
X-API-TOKEN: <api_token>
```

## API端点详细规格

### 1. 用户登录

**端点**: `POST /account/login`

**请求格式**:
```json
{
  "password": "string (必填)"
}
```

**请求示例**:
```json
{
  "password": "mypassword"
}
```

**响应格式**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expire_in": 86400,
  "id": "coco-default-user",
  "status": "ok",
  "username": "coco-default-user"
}
```

**状态码**:
- `200 OK`: 登录成功
- `400 Bad Request`: 密码为空
- `403 Forbidden`: 密码错误
- `500 Internal Server Error`: 服务器内部错误

### 2. 获取用户资料

**端点**: `GET /account/profile`

**认证**: 需要Bearer Token或API Token

**响应格式**:
```json
{
  "id": "coco-default-user",
  "username": "coco-default-user",
  "email": "",
  "roles": ["admin"]
}
```

**状态码**:
- `200 OK`: 获取成功
- `401 Unauthorized`: 认证失败
- `500 Internal Server Error`: 服务器内部错误

### 3. 请求访问令牌

**端点**: `POST /auth/request_access_token`

**认证**: 需要Bearer Token

**请求格式**:
```json
{
  "name": "string (可选, 令牌名称)"
}
```

**响应格式**:
```json
{
  "access_token": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
  "expire_in": **********,
  "id": "token-id",
  "name": "token_1735603199_abc12345"
}
```

**状态码**:
- `200 OK`: 创建成功
- `401 Unauthorized`: 认证失败
- `500 Internal Server Error`: 服务器内部错误

### 4. 获取访问令牌列表

**端点**: `GET /auth/access_token/_cat`

**认证**: 需要Bearer Token

**响应格式**:
```json
[
  {
    "id": "token-id-1",
    "name": "token_1735603199_abc12345",
    "access_token": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
    "expire_in": **********,
    "created": "2025-01-15T10:30:00Z"
  }
]
```

**状态码**:
- `200 OK`: 获取成功
- `401 Unauthorized`: 认证失败
- `500 Internal Server Error`: 服务器内部错误

### 5. 删除访问令牌

**端点**: `DELETE /auth/access_token/{token_id}`

**认证**: 需要Bearer Token

**路径参数**:
- `token_id`: 令牌ID (必填)

**响应格式**:
```json
{
  "result": "deleted",
  "id": "token-id"
}
```

**状态码**:
- `200 OK`: 删除成功
- `401 Unauthorized`: 认证失败
- `403 Forbidden`: 权限不足
- `404 Not Found`: 令牌不存在
- `500 Internal Server Error`: 服务器内部错误

### 6. 重命名访问令牌

**端点**: `POST /auth/access_token/{token_id}/_rename`

**认证**: 需要Bearer Token

**路径参数**:
- `token_id`: 令牌ID (必填)

**请求格式**:
```json
{
  "name": "string (必填, 新的令牌名称)"
}
```

**响应格式**:
```json
{
  "result": "updated",
  "id": "token-id"
}
```

**状态码**:
- `200 OK`: 重命名成功
- `400 Bad Request`: 名称为空
- `401 Unauthorized`: 认证失败
- `403 Forbidden`: 权限不足
- `404 Not Found`: 令牌不存在
- `500 Internal Server Error`: 服务器内部错误

### 7. 用户登出

**端点**: `POST /account/logout`

**认证**: 需要Bearer Token

**响应格式**:
```json
{
  "status": "ok",
  "message": "Logged out successfully"
}
```

**状态码**:
- `200 OK`: 登出成功
- `401 Unauthorized`: 认证失败
- `500 Internal Server Error`: 服务器内部错误

## 数据模型

### UserClaims (JWT Claims)
```rust
{
  "user_id": "string",
  "username": "string", 
  "roles": ["string"],
  "exp": "number (Unix timestamp)"
}
```

### AccessToken
```rust
{
  "id": "string",
  "access_token": "string",
  "name": "string",
  "user_id": "string",
  "expire_in": "number (Unix timestamp)",
  "created": "string (ISO 8601)",
  "roles": ["string"]
}
```

### UserProfile
```rust
{
  "id": "string",
  "username": "string",
  "email": "string",
  "roles": ["string"]
}
```

## 错误响应格式

### 标准错误响应
```json
{
  "error": "string (错误类型)",
  "message": "string (错误描述)"
}
```

### 认证错误
```json
{
  "error": "Missing authentication token",
  "message": "缺少API令牌。请在请求头中提供X-API-TOKEN。"
}
```

### 权限错误
```json
{
  "error": "Permission denied",
  "message": "权限不足，无法执行此操作。"
}
```

## 安全考虑

### JWT Token
- 使用HS256算法签名
- 24小时过期时间
- 包含用户ID、用户名、角色信息

### API Token
- 365天过期时间
- UUID + 随机字符串格式
- 存储在数据库中，支持撤销

### 密码安全
- 使用bcrypt哈希存储
- 支持密码强度验证

## 公开端点

以下端点无需认证：
- `GET /health` - 健康检查
- `POST /account/login` - 用户登录
- `POST /setup/_initialize` - 系统初始化

## 测试用例

### 登录测试
```bash
curl -X POST http://localhost:9000/account/login \
  -H "Content-Type: application/json" \
  -d '{"password": "mypassword"}'
```

### 获取用户资料测试
```bash
curl -X GET http://localhost:9000/account/profile \
  -H "Authorization: Bearer <access_token>"
```

### 创建API Token测试
```bash
curl -X POST http://localhost:9000/auth/request_access_token \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{"name": "my-api-token"}'
```

### 使用API Token测试
```bash
curl -X GET http://localhost:9000/account/profile \
  -H "X-API-TOKEN: <api_token>"
```
