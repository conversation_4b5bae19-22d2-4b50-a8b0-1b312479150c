use crate::auth::permission_checker::{Permission, Permission<PERSON><PERSON><PERSON>, UserPermissions};
use crate::config::config_manager::ConfigManager;
use crate::handlers::account_handler::UserClaims;
use axum::{
    extract::State,
    http::{Request, StatusCode},
    middleware::Next,
    response::{IntoResponse, Response},
    Json,
};
use jsonwebtoken::{decode, DecodingKey, Validation};
use serde_json::json;
use std::sync::Arc;
use tracing::{error, info, warn};

// 身份验证中间件
pub async fn auth_middleware<B>(
    request: Request<B>,
    next: Next<B>,
) -> Result<Response, impl IntoResponse>
where
    B: Send,
{
    info!("Processing authentication for request to {}", request.uri());

    // 检查是否需要跳过身份验证的路径
    let path = request.uri().path();
    if should_skip_auth(path) {
        return Ok(next.run(request).await);
    }

    // 从请求头中获取API令牌
    // 支持两种格式：X-API-TOKEN 和 Authorization: Bearer
    let token = request
        .headers()
        .get("X-API-TOKEN")
        .and_then(|value| value.to_str().ok())
        .or_else(|| {
            // 检查Authorization头中的Bearer令牌
            request
                .headers()
                .get("Authorization")
                .and_then(|value| value.to_str().ok())
                .and_then(|auth_header| {
                    if auth_header.starts_with("Bearer ") {
                        Some(&auth_header[7..]) // 移除"Bearer "前缀
                    } else {
                        None
                    }
                })
        });

    // 验证令牌（在实际应用中应该从配置或数据库中获取有效令牌列表）
    match token {
        Some(token) => match is_valid_token(token) {
            Ok(true) => {
                info!("Authentication successful for token: {}", mask_token(token));
                Ok(next.run(request).await)
            }
            Ok(false) => {
                info!("Authentication failed: invalid token provided");
                let error_response = json!({
                    "error": "Invalid authentication token",
                    "message": "提供的API令牌无效。请检查令牌是否正确。"
                });
                Err((StatusCode::UNAUTHORIZED, Json(error_response)))
            }
            Err(error_msg) => {
                info!(
                    "Authentication failed: invalid token provided - {}",
                    error_msg
                );
                let error_response = json!({
                    "error": "Invalid authentication token",
                    "message": format!("提供的API令牌无效: {}。请检查令牌是否正确。", error_msg)
                });
                Err((StatusCode::UNAUTHORIZED, Json(error_response)))
            }
        },
        None => {
            info!("Authentication failed: missing token");
            let error_response = json!({
                "error": "Missing authentication token",
                "message": "缺少API令牌。请在请求头中提供X-API-TOKEN。"
            });
            Err((StatusCode::UNAUTHORIZED, Json(error_response)))
        }
    }
}

// 检查是否应该跳过身份验证的路径
fn should_skip_auth(path: &str) -> bool {
    // /setup/_initialize, /health 和 /account/login 端点不需要身份验证
    path == "/setup/_initialize" || path == "/health" || path == "/account/login"
}

// 验证令牌是否有效（支持JWT令牌和硬编码令牌）
fn is_valid_token(token: &str) -> Result<bool, &'static str> {
    // 首先尝试验证JWT令牌
    let secret = "coco-server-secret-key";
    match decode::<UserClaims>(
        token,
        &DecodingKey::from_secret(secret.as_ref()),
        &Validation::default(),
    ) {
        Ok(_) => return Ok(true),
        Err(_) => {
            // 如果JWT验证失败，尝试硬编码令牌列表
            const VALID_TOKENS: &[&str] = &["test-token-1", "test-token-2", "abc123"];

            if VALID_TOKENS.contains(&token) {
                Ok(true)
            } else {
                // 检查令牌是否格式正确但无效
                if token.len() > 0 {
                    Err("令牌格式正确但未在有效令牌列表中")
                } else {
                    Err("令牌为空")
                }
            }
        }
    }
}

// 遮蔽令牌以用于日志记录（只显示前几个字符和后几个字符）
fn mask_token(token: &str) -> String {
    if token.len() <= 4 {
        "*".repeat(token.len())
    } else {
        let prefix = &token[..2];
        let suffix = &token[token.len() - 2..];
        format!("{}***{}", prefix, suffix)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use axum::{
        body::Body,
        http::{Request, StatusCode},
        middleware::from_fn,
        routing::get,
        Router,
    };
    use tower::ServiceExt;

    #[tokio::test]
    async fn test_auth_middleware_allows_setup_initialize() {
        let app = Router::new()
            .route("/setup/_initialize", get(|| async { "ok" }))
            .route_layer(from_fn(auth_middleware));

        let request = Request::builder()
            .uri("/setup/_initialize")
            .body(Body::empty())
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);
    }

    #[tokio::test]
    async fn test_auth_middleware_blocks_without_token() {
        let app = Router::new()
            .route("/test", get(|| async { "ok" }))
            .route_layer(from_fn(auth_middleware));

        let request = Request::builder().uri("/test").body(Body::empty()).unwrap();

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::UNAUTHORIZED);
    }

    #[tokio::test]
    async fn test_auth_middleware_allows_with_valid_token() {
        let app = Router::new()
            .route("/test", get(|| async { "ok" }))
            .route_layer(from_fn(auth_middleware));

        let request = Request::builder()
            .uri("/test")
            .header("X-API-TOKEN", "test-token-1")
            .body(Body::empty())
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);
    }

    #[tokio::test]
    async fn test_auth_middleware_allows_with_bearer_token() {
        let app = Router::new()
            .route("/test", get(|| async { "ok" }))
            .route_layer(from_fn(auth_middleware));

        let request = Request::builder()
            .uri("/test")
            .header("Authorization", "Bearer test-token-1")
            .body(Body::empty())
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);
    }

    #[tokio::test]
    async fn test_auth_middleware_blocks_with_invalid_token() {
        let app = Router::new()
            .route("/test", get(|| async { "ok" }))
            .route_layer(from_fn(auth_middleware));

        let request = Request::builder()
            .uri("/test")
            .header("X-API-TOKEN", "invalid-token")
            .body(Body::empty())
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::UNAUTHORIZED);
    }
}
